<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار WebSocket</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .log {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            margin: 10px 0;
            font-family: monospace;
        }
        .controls {
            margin: 10px 0;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a87;
        }
        input[type="text"] {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار WebSocket للتوافق مع الباك إند</h1>
        
        <div id="status" class="status disconnected">غير متصل</div>
        
        <div class="controls">
            <button onclick="connect()">اتصال</button>
            <button onclick="disconnect()">قطع الاتصال</button>
            <button onclick="sendHi()">إرسال Hi</button>
            <button onclick="sendOnline()">إرسال Online</button>
            <button onclick="sendGuest()">إرسال Guest</button>
        </div>
        
        <div class="controls">
            <input type="text" id="messageInput" placeholder="اكتب رسالة..." />
            <button onclick="sendCustomMessage()">إرسال رسالة مخصصة</button>
        </div>
        
        <div class="controls">
            <input type="text" id="usernameInput" placeholder="اسم المستخدم" value="TestUser" />
            <button onclick="sendBroadcast()">إرسال بث عام</button>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let ws = null;
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(connected) {
            const statusDiv = document.getElementById('status');
            if (connected) {
                statusDiv.className = 'status connected';
                statusDiv.textContent = 'متصل';
            } else {
                statusDiv.className = 'status disconnected';
                statusDiv.textContent = 'غير متصل';
            }
        }
        
        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('الاتصال موجود بالفعل');
                return;
            }
            
            const url = `ws://${location.host}/socket.io/?EIO=4&transport=websocket`;
            log(`محاولة الاتصال بـ: ${url}`);
            
            ws = new WebSocket(url);
            
            ws.onopen = function(event) {
                log('✅ تم الاتصال بنجاح');
                updateStatus(true);
            };
            
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    log(`📨 رسالة واردة: ${JSON.stringify(data, null, 2)}`);
                } catch (error) {
                    log(`📨 رسالة واردة (نص): ${event.data}`);
                }
            };
            
            ws.onclose = function(event) {
                log(`❌ تم قطع الاتصال: ${event.code} - ${event.reason}`);
                updateStatus(false);
            };
            
            ws.onerror = function(error) {
                log(`🚨 خطأ في الاتصال: ${error}`);
                updateStatus(false);
            };
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
                log('تم قطع الاتصال يدوياً');
                updateStatus(false);
            }
        }
        
        function sendMessage(cmd, data) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ لا يوجد اتصال');
                return;
            }
            
            const message = {
                cmd: cmd,
                data: data
            };
            
            const messageStr = JSON.stringify(message);
            ws.send(messageStr);
            log(`📤 تم إرسال: ${messageStr}`);
        }
        
        function sendHi() {
            sendMessage('hi', 'test');
        }
        
        function sendOnline() {
            sendMessage('online', {
                p: 'test_fingerprint'
            });
        }
        
        function sendGuest() {
            const username = document.getElementById('usernameInput').value || 'TestUser';
            sendMessage('g', {
                username: username,
                encr: 'test_encrypted_data',
                fp: 'test_fingerprint',
                code: 'SA'
            });
        }
        
        function sendCustomMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            if (!message) {
                log('❌ الرسالة فارغة');
                return;
            }
            
            sendMessage('msg', {
                msg: message
            });
            
            messageInput.value = '';
        }
        
        function sendBroadcast() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim() || 'رسالة اختبار';
            
            sendMessage('bc', {
                msg: message,
                link: ''
            });
        }
        
        // اتصال تلقائي عند تحميل الصفحة
        window.onload = function() {
            log('🚀 تم تحميل صفحة الاختبار');
            log('انقر على "اتصال" لبدء الاختبار');
        };
    </script>
</body>
</html>
